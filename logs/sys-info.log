2025-08-05 09:33:45 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-05 09:33:45 [main] INFO  c.transitsync.TransitSyncApplication - Starting TransitSyncApplication using Java 17.0.8 with PID 20132 (D:\TransitSync\TransitSync-be\transitsync-admin\target\classes started by Administrator in D:\TransitSync)
2025-08-05 09:33:45 [main] INFO  c.transitsync.TransitSyncApplication - The following 1 profile is active: "dev"
2025-08-05 09:33:51 [main] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-05 09:33:51 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2025-08-05 09:33:51 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-08-05 09:34:09 [main] INFO  com.zaxxer.hikari.pool.HikariPool - master - Added connection com.mysql.cj.jdbc.ConnectionImpl@64b20d9c
2025-08-05 09:34:09 [main] INFO  com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-08-05 09:34:09 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-08-05 09:34:09 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-05 09:34:13 [main] INFO  o.d.common.redis.config.RedisConfig - 初始化 redis 配置
2025-08-05 09:34:13 [main] INFO  org.redisson.Version - Redisson 3.50.0
2025-08-05 09:34:13 [redisson-netty-1-5] INFO  o.r.connection.ConnectionsHolder - 1 connections initialized for localhost/127.0.0.1:6379
2025-08-05 09:34:13 [redisson-netty-1-4] INFO  o.r.connection.ConnectionsHolder - 8 connections initialized for localhost/127.0.0.1:6379
2025-08-05 09:34:16 [main] INFO  o.d.common.json.config.JacksonConfig - 初始化 jackson 配置
2025-08-05 09:34:17 [main] INFO  c.b.m.e.s.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@aed0151
2025-08-05 09:34:24 [main] INFO  o.d.w.p.modes.sb.config.BeanConfig - 【warm-flow】，加载完成
2025-08-05 09:34:25 [main] INFO  io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-05 09:34:25 [main] INFO  org.xnio - XNIO version 3.8.16.Final
2025-08-05 09:34:25 [main] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-05 09:34:25 [main] INFO  org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-05 09:34:25 [main] INFO  c.transitsync.TransitSyncApplication - Started TransitSyncApplication in 41.277 seconds (process running for 42.527)
2025-08-05 09:34:25 [main] INFO  o.d.c.sse.listener.SseTopicListener - 初始化SSE主题订阅监听器成功
2025-08-05 09:34:26 [main] INFO  o.d.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2025-08-05 09:34:26 [RMI TCP Connection(5)-192.168.91.1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05 09:36:33 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-08-05 09:36:33 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-08-05 09:36:33 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[2]毫秒
2025-08-05 09:36:33 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-08-05 09:36:34 [XNIO-1 task-3] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[439]毫秒
2025-08-05 09:36:34 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/code],耗时:[65]毫秒
2025-08-05 09:36:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"username":"admin","password":"admin123","rememberMe":true,"clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-08-05 09:36:35 [schedule-pool-1] INFO  o.d.s.s.i.SysLogininforServiceImpl - [127.0.0.1]内网IP[admin][Success][登录成功]
2025-08-05 09:36:35 [XNIO-1 task-4] INFO  o.d.web.listener.UserActionListener - user doLogin, userId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJDZFJLQzJVQW9Rb3BsSFJQc2hRb3U1OVFSMWZKR0EyQSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAzLCJkZXB0TmFtZSI6IueglOWPkemDqOmXqCIsImRlcHRDYXRlZ29yeSI6IiJ9.8qQ1TcnwGfdDaJfChmSjxvAtN1tVJGkfnLTK_ATWNOU
2025-08-05 09:36:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[POST /auth/login],耗时:[281]毫秒
2025-08-05 09:36:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-08-05 09:36:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[40]毫秒
2025-08-05 09:36:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-08-05 09:36:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[23]毫秒
2025-08-05 09:36:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-08-05 09:36:35 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[1]毫秒
2025-08-05 09:36:40 [schedule-pool-1] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录RuoYi-Vue-Plus后台管理系统
2025-08-05 09:36:40 [redisson-3-2] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录RuoYi-Vue-Plus后台管理系统
2025-08-05 09:36:50 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/user/getInfo],无参数
2025-08-05 09:36:50 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/user/getInfo],耗时:[7]毫秒
2025-08-05 09:36:50 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /system/menu/getRouters],无参数
2025-08-05 09:36:50 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /system/menu/getRouters],耗时:[10]毫秒
2025-08-05 09:36:50 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-08-05 09:36:50 [XNIO-1 task-4] INFO  o.d.c.w.i.PlusWebInvokeTimeInterceptor - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[0]毫秒
2025-08-05 09:42:41 [SpringApplicationShutdownHook] INFO  io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-05 09:42:41 [SpringApplicationShutdownHook] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-05 09:42:41 [SpringApplicationShutdownHook] INFO  o.d.c.core.config.ThreadPoolConfig - ====关闭后台任务任务线程池====
2025-08-05 09:42:41 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-08-05 09:42:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-08-05 09:42:41 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-08-05 09:42:41 [SpringApplicationShutdownHook] INFO  c.b.d.d.d.DefaultDataSourceDestroyer - dynamic-datasource close the datasource named [master] success,
2025-08-05 09:42:41 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
